import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import LeaveType from '@/models/leave/LeaveType';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * GET /api/leave/types/[id]
 * Get a specific leave type
 */
export async function GET(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get the ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid leave type ID format' },
        { status: 400 }
      );
    }
    // Get leave type
    const leaveType = await LeaveType.findById(id)
      .populate('applicableDepartments', 'name')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .lean();
    if (!leaveType) {
      return NextResponse.json(
        { error: 'Leave type not found' },
        { status: 404 }
      );
    }

    // Transform to client-safe format
    const transformedLeaveType = {
      id: leaveType._id.toString(),
      name: leaveType.name,
      code: leaveType.code,
      description: leaveType.description,
      defaultDays: leaveType.defaultDays,
      isActive: leaveType.isActive,
      isPaid: leaveType.isPaid,
      requiresApproval: leaveType.requiresApproval,
      maxConsecutiveDays: leaveType.maxConsecutiveDays,
      minNoticeInDays: leaveType.minNoticeInDays,
      allowCarryOver: leaveType.allowCarryOver,
      maxCarryOverDays: leaveType.maxCarryOverDays,
      color: leaveType.color,
      applicableRoles: leaveType.applicableRoles,
      applicableDepartments: leaveType.applicableDepartments?.map((dept: any) => dept._id?.toString() || dept),
      createdBy: leaveType.createdBy?._id?.toString() || leaveType.createdBy,
      updatedBy: leaveType.updatedBy?._id?.toString() || leaveType.updatedBy,
      createdAt: leaveType.createdAt,
      updatedAt: leaveType.updatedAt
    };

    return NextResponse.json({
      success: true,
      data: transformedLeaveType
    });
  } catch (error: unknown) {
    logger.error('Error getting leave type', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while fetching leave type' 
      },
      { status: 500 }
    );
  }
}
/**
 * PUT /api/leave/types/[id]
 * Update a leave type
 */
export async function PUT(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get the ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid leave type ID format' },
        { status: 400 }
      );
    }
    // Get request body
    const body = await req.json();
    // Check if leave type exists
    const existingLeaveType = await LeaveType.findById(id);
    if (!existingLeaveType) {
      return NextResponse.json(
        { error: 'Leave type not found' },
        { status: 404 }
      );
    }
    // If code is being updated, check for uniqueness
    if (body.code && body.code !== existingLeaveType.code) {
      const duplicateLeaveType = await LeaveType.findOne({ 
        code: body.code,
        _id: { $ne: id }
      });
      if (duplicateLeaveType) {
        return NextResponse.json(
          { error: `Leave type with code ${body.code} already exists` },
          { status: 400 }
        );
      }
    }
    // Set updated by
    body.updatedBy = user.id;
    // Update leave type
    const updatedLeaveType = await LeaveType.findByIdAndUpdate(
      id,
      body,
      { new: true, runValidators: true }
    ).populate('applicableDepartments', 'name')
     .populate('createdBy', 'firstName lastName')
     .populate('updatedBy', 'firstName lastName');

    // Transform to client-safe format
    const transformedLeaveType = {
      id: updatedLeaveType._id.toString(),
      name: updatedLeaveType.name,
      code: updatedLeaveType.code,
      description: updatedLeaveType.description,
      defaultDays: updatedLeaveType.defaultDays,
      isActive: updatedLeaveType.isActive,
      isPaid: updatedLeaveType.isPaid,
      requiresApproval: updatedLeaveType.requiresApproval,
      maxConsecutiveDays: updatedLeaveType.maxConsecutiveDays,
      minNoticeInDays: updatedLeaveType.minNoticeInDays,
      allowCarryOver: updatedLeaveType.allowCarryOver,
      maxCarryOverDays: updatedLeaveType.maxCarryOverDays,
      color: updatedLeaveType.color,
      applicableRoles: updatedLeaveType.applicableRoles,
      applicableDepartments: updatedLeaveType.applicableDepartments?.map((dept: any) => dept._id?.toString() || dept),
      createdBy: updatedLeaveType.createdBy?._id?.toString() || updatedLeaveType.createdBy,
      updatedBy: updatedLeaveType.updatedBy?._id?.toString() || updatedLeaveType.updatedBy,
      createdAt: updatedLeaveType.createdAt,
      updatedAt: updatedLeaveType.updatedAt
    };

    return NextResponse.json({
      success: true,
      message: 'Leave type updated successfully',
      data: transformedLeaveType
    });
  } catch (error: unknown) {
    logger.error('Error updating leave type', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while updating leave type' 
      },
      { status: 500 }
    );
  }
}
/**
 * DELETE /api/leave/types/[id]
 * Delete a leave type (soft delete by setting isActive to false)
 */
export async function DELETE(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get the ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid leave type ID format' },
        { status: 400 }
      );
    }
    // Check if leave type exists
    const existingLeaveType = await LeaveType.findById(id);
    if (!existingLeaveType) {
      return NextResponse.json(
        { error: 'Leave type not found' },
        { status: 404 }
      );
    }
    // Check if leave type is being used in any leave requests
    const Leave = (await import('@/models/leave/Leave')).default;
    const activeLeaveRequests = await Leave.countDocuments({
      leaveTypeId: id,
      status: { $in: ['pending', 'approved'] }
    });
    if (activeLeaveRequests > 0) {
      return NextResponse.json(
        { 
          error: `Cannot delete leave type. It is currently being used in ${activeLeaveRequests} active leave request(s)` 
        },
        { status: 400 }
      );
    }
    // Soft delete by setting isActive to false
    const deletedLeaveType = await LeaveType.findByIdAndUpdate(
      id,
      {
        isActive: false,
        updatedBy: user.id
      },
      { new: true }
    );

    // Transform to client-safe format
    const transformedLeaveType = {
      id: deletedLeaveType._id.toString(),
      name: deletedLeaveType.name,
      code: deletedLeaveType.code,
      description: deletedLeaveType.description,
      defaultDays: deletedLeaveType.defaultDays,
      isActive: deletedLeaveType.isActive,
      isPaid: deletedLeaveType.isPaid,
      requiresApproval: deletedLeaveType.requiresApproval,
      maxConsecutiveDays: deletedLeaveType.maxConsecutiveDays,
      minNoticeInDays: deletedLeaveType.minNoticeInDays,
      allowCarryOver: deletedLeaveType.allowCarryOver,
      maxCarryOverDays: deletedLeaveType.maxCarryOverDays,
      color: deletedLeaveType.color,
      applicableRoles: deletedLeaveType.applicableRoles,
      applicableDepartments: deletedLeaveType.applicableDepartments,
      createdBy: deletedLeaveType.createdBy.toString(),
      updatedBy: deletedLeaveType.updatedBy?.toString(),
      createdAt: deletedLeaveType.createdAt,
      updatedAt: deletedLeaveType.updatedAt
    };

    return NextResponse.json({
      success: true,
      message: 'Leave type deleted successfully',
      data: transformedLeaveType
    });
  } catch (error: unknown) {
    logger.error('Error deleting leave type', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while deleting leave type' 
      },
      { status: 500 }
    );
  }
}