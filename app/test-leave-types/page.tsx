"use client"

import { useEffect, useState } from 'react';

export default function TestLeaveTypesPage() {
  const [leaveTypes, setLeaveTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchLeaveTypes() {
      try {
        console.log('Fetching leave types...');
        const response = await fetch('/api/leave/types?activeOnly=true');
        console.log('Response status:', response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('Raw API response:', result);
        
        if (result.data && Array.isArray(result.data)) {
          console.log('Leave types data:', result.data);
          setLeaveTypes(result.data);
        } else {
          console.log('No data array found in response');
          setLeaveTypes([]);
        }
      } catch (err) {
        console.error('Error fetching leave types:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchLeaveTypes();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Leave Types Test</h1>
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold">Raw Data:</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(leaveTypes, null, 2)}
          </pre>
        </div>
        
        <div>
          <h2 className="text-lg font-semibold">Rendered Options:</h2>
          <select className="border p-2 w-full">
            <option value="">Select a leave type</option>
            {leaveTypes.map((leaveType) => {
              console.log('Rendering option:', leaveType);
              return (
                <option
                  key={leaveType.id || leaveType._id}
                  value={leaveType.id || leaveType._id}
                >
                  {leaveType.name} ({leaveType.code}) - ID: {leaveType.id || leaveType._id}
                </option>
              );
            })}
          </select>
        </div>
      </div>
    </div>
  );
}
