// Simple script to create a test leave type
const fetch = require('node-fetch');

async function createLeaveType() {
  try {
    const response = await fetch('http://localhost:3001/api/leave/types', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // You'll need to add authentication headers here
      },
      body: JSON.stringify({
        name: 'Annual Leave',
        code: 'ANNUAL',
        description: 'Yearly vacation leave for rest and recreation',
        defaultDays: 21,
        isPaid: true,
        requiresApproval: true,
        maxConsecutiveDays: 14,
        minNoticeInDays: 7,
        allowCarryOver: true,
        maxCarryOverDays: 5,
        color: '#3B82F6',
        isActive: true
      })
    });

    const result = await response.json();
    console.log('Response:', result);
  } catch (error) {
    console.error('Error:', error);
  }
}

createLeaveType();
