// components\leave-management\leave-request-form.tsx
"use client"

import { useState, useEffect } from "react"
import { zodR<PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { format, differenceInBusinessDays, addDays } from "date-fns"
import { Loader2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { IndependentDateRangePicker, DateRange } from "@/components/ui/independent-date-range-picker"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { useLeaveTypes } from "@/hooks/use-leave-types"
import { useLeaveRequests } from "@/hooks/use-leave-requests"
import { useEmployees, Employee } from "@/hooks/use-employees"
import { useEmployeeLeaveBalances } from "@/hooks/use-employee-leave-balances"
import { EmployeeSelector } from "@/components/ui/employee-selector"
import { EmployeeLeaveStats } from "@/components/leave-management/employee-leave-stats"
import { LeaveType } from "@/types/leave"
import { CreateLeaveRequestPayload } from "@/types/leave-request"

interface LeaveRequestFormProps {
  employeeId?: string // Optional - if provided, will pre-select this employee
  onSuccess?: () => void
  onCancel?: () => void
  onEmployeeChange?: (employeeId: string) => void // Callback when employee selection changes
  className?: string
}

const formSchema = z.object({
  employeeId: z.string().min(1, {
    message: "Please select an employee",
  }),
  leaveTypeId: z.string().min(1, {
    message: "Please select a leave type",
  }),
  dateRange: z.object({
    from: z.date({
      required_error: "Please select a start date",
    }),
    to: z.date({
      required_error: "Please select an end date",
    }),
  }).refine(data => data.from && data.to && data.from <= data.to, {
    message: "End date must be after or equal to start date",
  }),
  reason: z.string().min(5, {
    message: "Reason must be at least 5 characters",
  }).max(500, {
    message: "Reason must not exceed 500 characters",
  }),
  attachments: z.array(z.string()).optional(),
  notes: z.string().max(500, {
    message: "Notes must not exceed 500 characters",
  }).optional(),
})

export function LeaveRequestForm({ employeeId, onSuccess, onCancel, onEmployeeChange, className }: LeaveRequestFormProps) {
  const [selectedLeaveType, setSelectedLeaveType] = useState<LeaveType | null>(null)
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)
  const [duration, setDuration] = useState<number | null>(null)
  const [validation, setValidation] = useState<{
    isValid: boolean;
    messages: string[];
    noticeValid: boolean;
    durationValid: boolean;
    daysNotice?: number;
  }>({ isValid: true, messages: [], noticeValid: true, durationValid: true })
  const { toast } = useToast()

  const { leaveTypes, loading, error: leaveTypesError } = useLeaveTypes({
    activeOnly: true,
    autoFetch: true
  });



  const { createRequest, loading: submitting } = useLeaveRequests({ autoFetch: false });

  const { employees, loading: employeesLoading, searchEmployees, getEmployeeById } = useEmployees({
    autoFetch: true,
    status: 'active'
  });

  const { balances, loading: balancesLoading, fetchBalances } = useEmployeeLeaveBalances();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      employeeId: employeeId || "",
      leaveTypeId: "",
      reason: "",
      notes: "",
      attachments: [],
    },
  })

  const { dateRange } = form.watch()

  useEffect(() => {
    if (dateRange?.from && dateRange?.to) {
      // Calculate business days between start and end dates (inclusive)
      const days = differenceInBusinessDays(addDays(dateRange.to, 1), dateRange.from)
      setDuration(days > 0 ? days : 0)
    } else {
      setDuration(null)
    }
  }, [dateRange])

  // Validate notice period and duration whenever dates or leave type changes
  useEffect(() => {
    if (dateRange?.from && dateRange?.to && selectedLeaveType && duration !== null) {
      const today = new Date()
      const startDate = dateRange.from
      const daysNotice = differenceInBusinessDays(startDate, today)

      const messages: string[] = []
      let noticeValid = true
      let durationValid = true

      // Check minimum notice period
      if (selectedLeaveType.minNoticeInDays > 0) {
        if (daysNotice < selectedLeaveType.minNoticeInDays) {
          noticeValid = false
          messages.push(`❌ ${selectedLeaveType.name} requires at least ${selectedLeaveType.minNoticeInDays} business days notice. You have provided ${daysNotice} business days.`)
        } else {
          messages.push(`✓ Notice period satisfied (${daysNotice} business days provided, ${selectedLeaveType.minNoticeInDays} required)`)
        }
      }

      // Check maximum consecutive days
      if (selectedLeaveType.maxConsecutiveDays > 0) {
        if (duration > selectedLeaveType.maxConsecutiveDays) {
          durationValid = false
          messages.push(`❌ ${selectedLeaveType.name} allows maximum ${selectedLeaveType.maxConsecutiveDays} consecutive days. You have requested ${duration} days.`)
        } else {
          messages.push(`✓ Duration within limits (${duration} days requested, ${selectedLeaveType.maxConsecutiveDays} maximum allowed)`)
        }
      }

      setValidation({
        isValid: noticeValid && durationValid,
        messages,
        noticeValid,
        durationValid,
        daysNotice
      })
    } else {
      setValidation({ isValid: true, messages: [], noticeValid: true, durationValid: true })
    }
  }, [dateRange?.from, dateRange?.to, selectedLeaveType, duration])

  // Watch for leave type changes
  const watchedLeaveTypeId = form.watch('leaveTypeId')

  useEffect(() => {
    if (watchedLeaveTypeId) {
      const leaveType = leaveTypes.find(lt => lt.id === watchedLeaveTypeId)
      setSelectedLeaveType(leaveType || null)
    } else {
      setSelectedLeaveType(null)
    }
  }, [watchedLeaveTypeId, leaveTypes])

  // Watch for employee changes
  const watchedEmployeeId = form.watch('employeeId')

  useEffect(() => {
    if (watchedEmployeeId) {
      const employee = getEmployeeById(watchedEmployeeId)
      setSelectedEmployee(employee || null)
      // Fetch leave balances for selected employee
      fetchBalances(watchedEmployeeId)
      // Notify parent component of employee change
      onEmployeeChange?.(watchedEmployeeId)
    } else {
      setSelectedEmployee(null)
    }
  }, [watchedEmployeeId, getEmployeeById, fetchBalances, onEmployeeChange])

  // Pre-select employee if provided via props
  useEffect(() => {
    if (employeeId && employees.length > 0) {
      const employee = getEmployeeById(employeeId)
      if (employee) {
        form.setValue('employeeId', employeeId)
        setSelectedEmployee(employee)
      }
    }
  }, [employeeId, employees, getEmployeeById, form])

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // Debug logging
    console.log('Form submission values:', values);
    console.log('Selected leave type:', selectedLeaveType);

    const payload: CreateLeaveRequestPayload = {
      employeeId: values.employeeId,
      leaveTypeId: values.leaveTypeId,
      startDate: format(values.dateRange.from, 'yyyy-MM-dd'),
      endDate: format(values.dateRange.to, 'yyyy-MM-dd'),
      reason: values.reason,
      notes: values.notes || undefined,
      attachments: values.attachments?.length ? values.attachments : undefined,
    };

    const result = await createRequest(payload);

    if (result) {
      if (onSuccess) {
        onSuccess();
      }
      form.reset();
      setSelectedEmployee(null);
      setSelectedLeaveType(null);
      setDuration(null);
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Request Leave</CardTitle>
        <CardDescription>Submit a new leave request</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Employee Selection */}
            <FormField
              control={form.control}
              name="employeeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Employee</FormLabel>
                  <FormControl>
                    <EmployeeSelector
                      employees={employees}
                      value={field.value}
                      onValueChange={field.onChange}
                      onSearchChange={searchEmployees}
                      placeholder="Select an employee..."
                      loading={employeesLoading}
                      disabled={submitting}
                      showDepartment={true}
                      showEmployeeId={true}
                      filterStatus="active"
                      className="w-full"
                    />
                  </FormControl>
                  <FormDescription>
                    Select the employee for whom you are creating this leave request
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="leaveTypeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Leave Type</FormLabel>
                  <FormControl>
                    <select
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={field.value || ""}
                      onChange={(e) => {
                        console.log('HTML select onChange called with:', e.target.value);
                        field.onChange(e.target.value);
                      }}
                      disabled={loading || submitting}
                    >
                      <option value="">Select a leave type</option>
                      {loading ? (
                        <option disabled>Loading leave types...</option>
                      ) : leaveTypesError ? (
                        <option disabled>Error loading leave types</option>
                      ) : leaveTypes.length === 0 ? (
                        <option disabled>No leave types available</option>
                      ) : (
                        leaveTypes.map((leaveType) => {
                          console.log('Rendering option for leave type:', leaveType);
                          console.log('Leave type ID:', leaveType.id);
                          console.log('Leave type name:', leaveType.name);
                          console.log('Leave type code:', leaveType.code);
                          return (
                            <option
                              key={leaveType.id || `${leaveType.name}-${leaveType.code}`}
                              value={leaveType.id || `${leaveType.name} (${leaveType.code})`}
                            >
                              {leaveType.name} ({leaveType.code})
                            </option>
                          );
                        })
                      )}
                    </select>
                  </FormControl>
                  {selectedLeaveType && (
                    <FormDescription>
                      {selectedLeaveType.isPaid ? "Paid leave" : "Unpaid leave"}
                      {selectedLeaveType.maxConsecutiveDays > 0 &&
                        ` • Max ${selectedLeaveType.maxConsecutiveDays} consecutive days`}
                      {selectedLeaveType.minNoticeInDays > 0 &&
                        ` • ${selectedLeaveType.minNoticeInDays} days notice required`}
                      {selectedLeaveType.defaultDays > 0 &&
                        ` • Default allocation: ${selectedLeaveType.defaultDays} days`}
                    </FormDescription>
                  )}
                  {!loading && leaveTypes.length === 0 && (
                    <FormDescription className="text-yellow-600">
                      No leave types are available. Please contact your administrator to set up leave types before creating requests.
                    </FormDescription>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dateRange"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Leave Dates</FormLabel>
                  <FormControl>
                    <IndependentDateRangePicker
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Select your leave dates"
                      minDate={new Date()} // Prevent selecting past dates
                      disabledDates={(date) => {
                        // Disable weekends
                        if (date.getDay() === 0 || date.getDay() === 6) return true;
                        return false;
                      }}
                      labels={{
                        startDate: "Start Date",
                        endDate: "End Date",
                        clear: "Clear Dates"
                      }}
                      clearable={true}
                      allowSingleDate={false} // Require both start and end dates
                      className="w-full"
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Select the start and end dates for your leave request. Each date can be selected independently.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Duration and Notice Validation Display */}
            {duration !== null && (
              <div className="space-y-2">
                <div className="bg-muted p-3 rounded-md text-sm">
                  Duration: <strong>{duration} working day{duration !== 1 ? 's' : ''}</strong>
                </div>

                {/* Validation Messages */}
                {validation.messages.length > 0 && (
                  <div className="space-y-2">
                    {validation.messages.map((message, index) => {
                      const isError = message.startsWith('❌')
                      return (
                        <div
                          key={index}
                          className={cn(
                            "p-3 rounded-md text-sm border",
                            isError
                              ? "bg-red-50 border-red-200 text-red-800"
                              : "bg-green-50 border-green-200 text-green-800"
                          )}
                        >
                          {message}
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            )}

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Please provide a reason for your leave request"
                      className="resize-none"
                      {...field}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Briefly explain why you are requesting leave
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional information"
                      className="resize-none"
                      {...field}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Any additional information that might be relevant
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* File upload functionality would go here */}
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={submitting}
        >
          Cancel
        </Button>
        <Button
          onClick={form.handleSubmit(onSubmit)}
          disabled={submitting || loading || leaveTypes.length === 0 || !validation.isValid}
        >
          {submitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : leaveTypes.length === 0 ? (
            "No Leave Types Available"
          ) : !validation.isValid ? (
            validation.noticeValid ? "Duration Exceeds Limit" : "Insufficient Notice Period"
          ) : (
            "Submit Request"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
