const mongoose = require('mongoose');

// Simple script to create a test leave type directly in the database
async function createTestLeaveType() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=resources');
    console.log('Connected to MongoDB');

    // Define the schema (simplified)
    const leaveTypeSchema = new mongoose.Schema({
      name: { type: String, required: true },
      code: { type: String, required: true, unique: true },
      description: String,
      defaultDays: { type: Number, required: true },
      isActive: { type: Boolean, default: true },
      isPaid: { type: Boolean, default: true },
      requiresApproval: { type: Boolean, default: true },
      maxConsecutiveDays: { type: Number, default: 0 },
      minNoticeInDays: { type: Number, default: 0 },
      allowCarryOver: { type: <PERSON>ole<PERSON>, default: false },
      maxCarryOverDays: { type: Number, default: 0 },
      color: { type: String, default: '#3B82F6' },
      createdBy: { type: mongoose.Schema.Types.ObjectId, required: true },
      updatedBy: mongoose.Schema.Types.ObjectId,
    }, { timestamps: true });

    const LeaveType = mongoose.models.LeaveType || mongoose.model('LeaveType', leaveTypeSchema);

    // Find a user to use as creator
    const userSchema = new mongoose.Schema({}, { strict: false });
    const User = mongoose.models.User || mongoose.model('User', userSchema);
    
    const user = await User.findOne({ role: { $in: ['super_admin', 'system_admin'] } });
    if (!user) {
      console.log('No admin user found');
      return;
    }

    console.log('Found admin user:', user._id);

    // Check if leave type already exists
    const existing = await LeaveType.findOne({ code: 'ANNUAL' });
    if (existing) {
      console.log('Annual leave type already exists:', existing);
      return;
    }

    // Create test leave type
    const leaveType = new LeaveType({
      name: 'Annual Leave',
      code: 'ANNUAL',
      description: 'Yearly vacation leave for rest and recreation',
      defaultDays: 21,
      isPaid: true,
      requiresApproval: true,
      maxConsecutiveDays: 14,
      minNoticeInDays: 7,
      allowCarryOver: true,
      maxCarryOverDays: 5,
      color: '#3B82F6',
      isActive: true,
      createdBy: user._id
    });

    await leaveType.save();
    console.log('Created leave type:', leaveType);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

createTestLeaveType();
